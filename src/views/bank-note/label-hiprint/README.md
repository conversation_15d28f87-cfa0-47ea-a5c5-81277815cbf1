# 标签打印设计器使用说明

## 概述

本模块基于 `vue-plugin-hiprint` 插件实现了一个完整的标签设计和打印解决方案，包括：

- 可视化标签设计器
- 模板管理系统
- 批量打印集成

## 文件结构

```
src/views/bank-note/label-hiprint/
├── index.vue                    # 主设计器页面
├── test.vue                     # 测试页面
├── api/
│   └── index.js                 # API 接口封装
├── components/
│   └── ElementLibrary.vue       # 元素库组件
└── README.md                    # 使用说明
```

## 核心功能

### 1. 标签设计器 (index.vue)

**主要功能：**
- 模板列表展示和管理
- 可视化设计界面
- 元素拖拽添加
- 模板保存和预览

**使用方法：**
1. 点击"新建模板"创建新模板
2. 从左侧元素库拖拽元素到设计区域
3. 在右侧属性面板编辑元素属性
4. 点击"保存模板"保存设计

### 2. 元素库 (ElementLibrary.vue)

**支持的元素类型：**
- 文本：普通文本显示
- 图片：图片显示
- 条形码：一维条形码
- 二维码：二维码
- 线条：分割线
- 矩形：边框和背景

**数据字段：**
- 基本信息：钱币编号、送评单号等
- 钱币信息：名称、年份、版别等
- 评级信息：分数、等级等
- 审核信息：审核状态、备注等

### 3. 批量打印集成

在 `src/views/bank-note/batch-print/components/print-config.vue` 中集成了模板选择功能：

- 支持系统模板和自定义模板切换
- 模板下拉选择
- 一键跳转到设计器
- 模板预览功能

## API 接口

### 模板管理
- `GET /api/label-design/templates` - 获取模板列表
- `POST /api/label-design/template` - 保存模板
- `PUT /api/label-design/template/{id}` - 更新模板
- `DELETE /api/label-design/template/{id}` - 删除模板
- `GET /api/label-design/template/{id}` - 获取模板详情

### 字段管理
- `GET /api/label-design/fields` - 获取可用字段
- `GET /api/label-design/fields/by-category` - 按分类获取字段

## 配置说明

### hiprint 配置 (src/utils/hiprint-config.js)

**主要配置项：**
```javascript
// 初始化配置
{
  host: '',        // 禁用WebSocket连接
  token: '',       // 认证令牌
  debug: false     // 调试模式
}

// 设计器配置
{
  grid: true,           // 显示网格
  gridColor: '#ddd',    // 网格颜色
  gridSize: 5,          // 网格大小
  showRuler: true,      // 显示标尺
  backgroundColor: '#f5f5f5'  // 背景色
}
```

### 元素模板配置

每种元素都有预定义的模板，包含默认的样式和属性：

```javascript
// 文本元素模板
{
  options: {
    left: 50,
    top: 50,
    height: 30,
    width: 200,
    title: '文本',
    textType: 'text',
    fontSize: 12,
    fontFamily: 'Microsoft YaHei',
    color: '#000000'
  }
}
```

## 常见问题

### 1. WebSocket 连接错误

**问题：** 控制台出现 WebSocket 连接失败的错误

**解决方案：** 这是正常现象，因为我们禁用了 WebSocket 连接以避免依赖本地打印服务。不影响设计器功能。

### 2. 模板保存失败

**可能原因：**
- 后端服务未启动
- 用户权限不足
- 模板数据格式错误

**解决方案：**
- 检查后端服务状态
- 确认用户具有 `banknote:label:design` 权限
- 检查模板数据是否完整

### 3. 元素拖拽无效

**可能原因：**
- hiprint 未正确初始化
- 设计器容器未找到

**解决方案：**
- 检查控制台错误信息
- 确认设计器容器元素存在
- 重新初始化设计器

## 开发指南

### 添加新的元素类型

1. 在 `src/utils/hiprint-config.js` 中添加元素模板：

```javascript
export const elementTemplates = {
  // 现有元素...
  newElement: {
    options: {
      // 元素属性配置
    }
  }
};
```

2. 在 `ElementLibrary.vue` 中添加元素定义：

```javascript
const basicElements = {
  // 现有元素...
  newElement: {
    name: '新元素',
    icon: SomeIcon,
    template: elementTemplates.newElement
  }
};
```

### 添加新的数据字段

1. 在后端 `LabelDesignServiceImpl` 中更新字段定义
2. 前端会自动从 API 获取最新的字段列表

### 自定义样式

主要样式文件位于各个 Vue 组件的 `<style>` 部分，可以根据需要进行自定义。

## 测试

使用测试页面 `test.vue` 可以快速验证功能：

1. 访问测试页面
2. 点击"测试 hiprint 插件"验证插件加载
3. 点击"测试 API 接口"验证后端连接

## 部署注意事项

1. **权限配置**：确保用户具有相应的权限
2. **数据库**：确保 `LABEL_TEMPLATE` 表已创建
3. **路由配置**：在后端菜单管理中添加相应菜单项
4. **静态资源**：确保 hiprint 相关资源正确加载

## 更新日志

- v1.0.0: 初始版本，包含基础设计器功能
- v1.0.1: 修复 WebSocket 连接错误
- v1.0.2: 添加元素库组件和拖拽功能
