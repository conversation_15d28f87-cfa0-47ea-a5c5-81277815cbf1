<template>
  <div class="api-explorer">
    <el-card>
      <template #header>
        <span>hiprint API 探索器</span>
      </template>
      
      <div class="explorer-actions">
        <el-button @click="exploreAPI" type="primary" :loading="exploring">
          探索 API
        </el-button>
        <el-button @click="testBasicUsage" type="success" :loading="testing">
          测试基础用法
        </el-button>
        <el-button @click="clearResults" type="info">
          清空结果
        </el-button>
      </div>
      
      <div class="results" v-if="results">
        <h4>探索结果：</h4>
        <div class="result-section" v-for="(section, key) in results" :key="key">
          <h5>{{ key }}:</h5>
          <pre>{{ formatResult(section) }}</pre>
        </div>
      </div>
      
      <div class="test-container" id="api-test-container" style="height: 300px; border: 1px solid #ddd; margin-top: 20px;">
        <!-- 测试容器 -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';

const exploring = ref(false);
const testing = ref(false);
const results = ref(null);

const formatResult = (data) => {
  if (typeof data === 'object') {
    return JSON.stringify(data, null, 2);
  }
  return String(data);
};

const exploreAPI = async () => {
  exploring.value = true;
  results.value = null;
  
  try {
    // 动态导入 hiprint
    const hiprintModule = await import('vue-plugin-hiprint');
    console.log('hiprint 模块:', hiprintModule);
    
    const { hiprint } = hiprintModule;
    
    // 初始化
    hiprint.init({
      host: '',
      token: '',
      debug: false
    });
    
    // 探索 hiprint 对象
    const hiprintInfo = {
      version: hiprint.version,
      methods: Object.getOwnPropertyNames(hiprint),
      prototype: Object.getOwnPropertyNames(Object.getPrototypeOf(hiprint)),
      PrintTemplate: typeof hiprint.PrintTemplate,
      PrintElementTypes: hiprint.PrintElementTypes || 'not found'
    };
    
    // 创建模板实例
    const template = new hiprint.PrintTemplate();
    
    const templateInfo = {
      constructor: template.constructor.name,
      methods: Object.getOwnPropertyNames(template),
      prototype: Object.getOwnPropertyNames(Object.getPrototypeOf(template)),
      panels: template.panels,
      panelsType: typeof template.panels,
      panelsIsArray: Array.isArray(template.panels)
    };
    
    // 尝试创建面板
    let panelInfo = null;
    try {
      if (typeof template.addPrintPanel === 'function') {
        const panel = template.addPrintPanel();
        panelInfo = {
          created: true,
          constructor: panel ? panel.constructor.name : 'null',
          methods: panel ? Object.getOwnPropertyNames(panel) : null,
          prototype: panel ? Object.getOwnPropertyNames(Object.getPrototypeOf(panel)) : null,
          type: typeof panel
        };
      } else {
        panelInfo = { error: 'addPrintPanel method not found' };
      }
    } catch (error) {
      panelInfo = { error: error.message };
    }
    
    // 检查元素类型
    let elementTypesInfo = null;
    try {
      // 常见的元素类型检查
      const commonTypes = ['text', 'image', 'barcode', 'qrcode', 'line', 'rect'];
      elementTypesInfo = {};
      
      for (const type of commonTypes) {
        elementTypesInfo[type] = {
          exists: hiprint[type] !== undefined,
          type: typeof hiprint[type]
        };
      }
      
      // 检查是否有元素工厂方法
      elementTypesInfo.factories = {
        createElement: typeof hiprint.createElement,
        newElement: typeof hiprint.newElement,
        addElement: typeof hiprint.addElement
      };
    } catch (error) {
      elementTypesInfo = { error: error.message };
    }
    
    results.value = {
      hiprintInfo,
      templateInfo,
      panelInfo,
      elementTypesInfo
    };
    
    EleMessage.success('API 探索完成');
    
  } catch (error) {
    results.value = { error: error.message, stack: error.stack };
    EleMessage.error('API 探索失败：' + error.message);
  } finally {
    exploring.value = false;
  }
};

const testBasicUsage = async () => {
  testing.value = true;
  
  try {
    // 导入 hiprint
    const { hiprint } = await import('vue-plugin-hiprint');
    
    // 初始化
    hiprint.init({
      host: '',
      token: '',
      debug: false
    });
    
    // 创建模板
    const template = new hiprint.PrintTemplate();
    console.log('模板创建成功:', template);
    
    // 尝试不同的方式添加元素
    const testElement = {
      options: {
        left: 50,
        top: 50,
        height: 30,
        width: 200,
        title: '测试文本',
        text: '测试内容'
      }
    };
    
    console.log('尝试添加元素:', testElement);
    
    // 方法1: 检查官方文档的用法
    try {
      // 根据 hiprint 文档，可能需要这样使用：
      if (typeof template.addPrintPanel === 'function') {
        const panel = template.addPrintPanel();
        console.log('面板创建成功:', panel);
        
        if (panel && typeof panel.addPrintText === 'function') {
          const textElement = panel.addPrintText(testElement);
          console.log('✅ 使用 panel.addPrintText 成功:', textElement);
        } else if (panel && typeof panel.addPrintElement === 'function') {
          const element = panel.addPrintElement(testElement);
          console.log('✅ 使用 panel.addPrintElement 成功:', element);
        } else {
          console.log('❌ 面板没有添加元素的方法');
        }
      }
    } catch (error) {
      console.error('面板方法失败:', error);
    }
    
    // 方法2: 尝试直接在模板上操作
    try {
      if (typeof template.addPrintText === 'function') {
        const textElement = template.addPrintText(testElement);
        console.log('✅ 使用 template.addPrintText 成功:', textElement);
      }
    } catch (error) {
      console.error('模板直接方法失败:', error);
    }
    
    // 尝试在容器中显示
    const container = document.getElementById('api-test-container');
    if (container && typeof template.design === 'function') {
      container.innerHTML = '';
      template.design(container, {
        grid: true,
        gridColor: '#ddd',
        gridSize: 5
      });
      console.log('✅ 设计器渲染成功');
    }
    
    EleMessage.success('基础用法测试完成，请查看控制台');
    
  } catch (error) {
    console.error('基础用法测试失败:', error);
    EleMessage.error('基础用法测试失败：' + error.message);
  } finally {
    testing.value = false;
  }
};

const clearResults = () => {
  results.value = null;
  const container = document.getElementById('api-test-container');
  if (container) {
    container.innerHTML = '';
  }
};
</script>

<style scoped>
.api-explorer {
  padding: 20px;
}

.explorer-actions {
  margin-bottom: 20px;
}

.explorer-actions .el-button {
  margin-right: 10px;
}

.results {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.result-section {
  margin-bottom: 16px;
}

.result-section h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.result-section pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
  font-size: 12px;
  margin: 0;
}

#api-test-container {
  background: #fff;
  border-radius: 4px;
}
</style>
