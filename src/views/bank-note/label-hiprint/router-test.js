// 临时路由配置 - 用于测试页面访问
// 在浏览器控制台中运行以下代码来添加临时路由

// 方法1: 如果使用 Vue Router
if (window.$router) {
  window.$router.addRoute({
    path: '/test/simple-test',
    name: 'SimpleTest',
    component: () => import('./simple-test.vue')
  });
  console.log('✅ 临时路由已添加: /test/simple-test');
}

// 方法2: 直接导航到测试页面
export function navigateToTest() {
  if (window.$router) {
    window.$router.push('/test/simple-test');
  } else {
    console.log('请在地址栏访问: /test/simple-test');
  }
}

// 方法3: 创建测试页面链接
export function createTestLinks() {
  const testPages = [
    { name: '简单测试', path: '/test/simple-test', file: 'simple-test.vue' },
    { name: 'API探索器', path: '/test/api-explorer', file: 'api-explorer.vue' },
    { name: '元素调试', path: '/test/element-debug', file: 'element-debug.vue' },
    { name: '元素测试', path: '/test/element-test', file: 'element-test.vue' }
  ];
  
  console.log('🔗 可用的测试页面:');
  testPages.forEach(page => {
    console.log(`${page.name}: ${window.location.origin}${page.path}`);
  });
  
  return testPages;
}

// 自动执行
if (typeof window !== 'undefined') {
  createTestLinks();
}
