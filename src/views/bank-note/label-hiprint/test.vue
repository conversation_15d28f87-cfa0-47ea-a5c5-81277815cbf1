<template>
  <div class="test-page">
    <el-card>
      <template #header>
        <span>vue-plugin-hiprint 测试页面</span>
      </template>

      <div class="test-content">
        <el-button @click="testHiprint" type="primary">测试 hiprint 插件</el-button>
        <el-button @click="testAPI" type="success">测试 API 接口</el-button>

        <div id="test-container" style="margin-top: 20px; height: 400px; border: 1px solid #ddd;">
          <!-- hiprint 测试容器 -->
        </div>

        <div class="api-test-result" v-if="apiResult">
          <h4>API 测试结果：</h4>
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { getTemplateList, getAvailableFields } from './api';
import {
  initHiprint as initHiprintPlugin,
  createPrintTemplate,
  designerConfig,
  handleHiprintError,
  elementTemplates
} from '@/utils/hiprint-config';

const apiResult = ref(null);

const testHiprint = async () => {
  try {
    // 初始化 hiprint
    await initHiprint();
    console.log('hiprint 初始化成功');

    // 创建一个简单的模板
    const template = createPrintTemplate();

    // 添加一个文本元素
    template.addPrintElement(elementTemplates.text);

    // 添加一个条形码元素
    const barcodeElement = { ...elementTemplates.barcode };
    barcodeElement.options.left = 50;
    barcodeElement.options.top = 100;
    template.addPrintElement(barcodeElement);

    // 在容器中显示
    const container = document.getElementById('test-container');
    if (container) {
      container.innerHTML = '';
      template.design(container, designerConfig);
    }

    EleMessage.success('hiprint 插件测试成功！');
  } catch (error) {
    const errorMessage = handleHiprintError(error);
    console.error('hiprint 测试失败:', error);
    EleMessage.error('hiprint 插件测试失败：' + errorMessage);
  }
};

const testAPI = async () => {
  try {
    // 测试获取模板列表
    const templates = await getTemplateList();
    console.log('模板列表:', templates);

    // 测试获取可用字段
    const fields = await getAvailableFields();
    console.log('可用字段:', fields);

    apiResult.value = {
      templates,
      fields,
      timestamp: new Date().toISOString()
    };

    EleMessage.success('API 测试成功！');
  } catch (error) {
    console.error('API 测试失败:', error);
    EleMessage.error('API 测试失败：' + error.message);
    apiResult.value = {
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-content {
  padding: 20px 0;
}

.api-test-result {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.api-test-result pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

#test-container {
  background: #fff;
  border-radius: 4px;
}
</style>
