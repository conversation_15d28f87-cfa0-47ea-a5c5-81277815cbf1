<template>
  <div class="simple-test">
    <el-card>
      <template #header>
        <span>简单元素添加测试</span>
      </template>
      
      <div class="test-actions">
        <el-button @click="runTest" type="primary" :loading="testing">
          运行测试
        </el-button>
        <el-button @click="clearTest" type="danger">
          清空测试
        </el-button>
      </div>
      
      <div class="test-log" v-if="logs.length > 0">
        <h4>测试日志：</h4>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
      
      <div class="test-container" id="simple-test-container" style="height: 400px; border: 1px solid #ddd; margin-top: 20px;">
        <!-- 设计器容器 -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';

const testing = ref(false);
const logs = ref([]);

const addLog = (type, message) => {
  logs.value.push({
    type,
    message,
    time: new Date().toLocaleTimeString()
  });
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const runTest = async () => {
  testing.value = true;
  logs.value = [];
  
  try {
    addLog('info', '开始运行简单测试...');
    
    // 步骤1: 导入 hiprint
    addLog('info', '步骤1: 导入 hiprint');
    const { hiprint } = await import('vue-plugin-hiprint');
    addLog('success', '✅ hiprint 导入成功');
    
    // 步骤2: 初始化 hiprint
    addLog('info', '步骤2: 初始化 hiprint');
    hiprint.init({
      host: '',
      token: '',
      debug: false
    });
    addLog('success', '✅ hiprint 初始化成功');
    
    // 步骤3: 创建模板
    addLog('info', '步骤3: 创建模板');
    const template = new hiprint.PrintTemplate();
    addLog('success', '✅ 模板创建成功');
    addLog('info', `模板对象: ${template.constructor.name}`);
    
    // 步骤4: 创建面板
    addLog('info', '步骤4: 创建面板');
    const panel = template.addPrintPanel();
    addLog('success', '✅ 面板创建成功');
    addLog('info', `面板对象: ${panel ? panel.constructor.name : 'null'}`);
    
    if (!panel) {
      addLog('error', '❌ 面板创建失败');
      return;
    }
    
    // 步骤5: 检查面板方法
    addLog('info', '步骤5: 检查面板方法');
    const panelMethods = Object.getOwnPropertyNames(panel);
    addLog('info', `面板方法数量: ${panelMethods.length}`);
    
    const importantMethods = ['addPrintText', 'addPrintElement', 'addElement'];
    importantMethods.forEach(method => {
      const exists = typeof panel[method] === 'function';
      addLog(exists ? 'success' : 'warning', `${method}: ${exists ? '存在' : '不存在'}`);
    });
    
    // 步骤6: 添加元素
    addLog('info', '步骤6: 添加文本元素');
    const elementConfig = {
      tid: 'simple-test-text',
      type: 'text',
      options: {
        left: 50,
        top: 50,
        width: 200,
        height: 30,
        text: '简单测试文本',
        fontSize: 14,
        color: '#000000'
      }
    };
    
    let addResult = null;
    let addMethod = null;
    
    // 尝试 addPrintText
    if (typeof panel.addPrintText === 'function') {
      try {
        addResult = panel.addPrintText(elementConfig);
        addMethod = 'addPrintText';
        addLog('success', '✅ 使用 addPrintText 成功');
      } catch (error) {
        addLog('error', `❌ addPrintText 失败: ${error.message}`);
      }
    }
    
    // 如果失败，尝试 addPrintElement
    if (!addResult && typeof panel.addPrintElement === 'function') {
      try {
        addResult = panel.addPrintElement(elementConfig);
        addMethod = 'addPrintElement';
        addLog('success', '✅ 使用 addPrintElement 成功');
      } catch (error) {
        addLog('error', `❌ addPrintElement 失败: ${error.message}`);
      }
    }
    
    if (addResult) {
      addLog('success', `✅ 元素添加成功，使用方法: ${addMethod}`);
      addLog('info', `返回结果: ${typeof addResult}`);
    } else {
      addLog('error', '❌ 所有添加方法都失败了');
    }
    
    // 步骤7: 验证元素
    addLog('info', '步骤7: 验证元素添加');
    
    // 检查面板元素
    const checkElements = () => {
      const elements1 = panel.printElements;
      const elements2 = panel.elements;
      
      addLog('info', `panel.printElements: ${elements1 ? (Array.isArray(elements1) ? `数组，长度${elements1.length}` : typeof elements1) : 'undefined'}`);
      addLog('info', `panel.elements: ${elements2 ? (Array.isArray(elements2) ? `数组，长度${elements2.length}` : typeof elements2) : 'undefined'}`);
      
      return (elements1 && Array.isArray(elements1) ? elements1.length : 0) + 
             (elements2 && Array.isArray(elements2) ? elements2.length : 0);
    };
    
    const elementCount = checkElements();
    addLog('info', `总元素数量: ${elementCount}`);
    
    // 步骤8: 渲染设计器
    addLog('info', '步骤8: 渲染设计器');
    const container = document.getElementById('simple-test-container');
    if (container && typeof template.design === 'function') {
      container.innerHTML = '';
      template.design(container, {
        grid: true,
        gridColor: '#ddd',
        gridSize: 5
      });
      addLog('success', '✅ 设计器渲染成功');
    } else {
      addLog('error', '❌ 设计器渲染失败');
    }
    
    // 步骤9: 最终检查
    addLog('info', '步骤9: 最终检查');
    setTimeout(() => {
      const finalCount = checkElements();
      addLog('info', `最终元素数量: ${finalCount}`);
      
      // 检查DOM中是否有元素
      const domElements = container.querySelectorAll('[data-tid], .hiprint-printElement');
      addLog('info', `DOM中的元素数量: ${domElements.length}`);
      
      if (domElements.length > 0) {
        addLog('success', '✅ 在DOM中找到了元素！');
      } else {
        addLog('warning', '⚠️ DOM中没有找到元素');
      }
    }, 500);
    
    addLog('success', '🎉 测试完成！');
    EleMessage.success('测试完成，请查看日志');
    
  } catch (error) {
    addLog('error', `❌ 测试失败: ${error.message}`);
    EleMessage.error('测试失败：' + error.message);
  } finally {
    testing.value = false;
  }
};

const clearTest = () => {
  logs.value = [];
  const container = document.getElementById('simple-test-container');
  if (container) {
    container.innerHTML = '';
  }
  EleMessage.info('测试已清空');
};
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.test-actions {
  margin-bottom: 20px;
}

.test-actions .el-button {
  margin-right: 10px;
}

.test-log {
  margin: 20px 0;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  padding: 4px 8px;
  margin-bottom: 2px;
  border-radius: 3px;
  display: flex;
  gap: 10px;
}

.log-item.info {
  background: #e6f7ff;
  color: #1890ff;
}

.log-item.success {
  background: #f6ffed;
  color: #52c41a;
}

.log-item.warning {
  background: #fffbe6;
  color: #faad14;
}

.log-item.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.log-time {
  font-weight: bold;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

#simple-test-container {
  background: #fff;
  border-radius: 4px;
}
</style>
