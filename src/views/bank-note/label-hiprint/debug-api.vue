<template>
  <div class="debug-api">
    <el-card>
      <template #header>
        <span>hiprint API 调试工具</span>
      </template>
      
      <div class="debug-actions">
        <el-button @click="debugAPI" type="primary" :loading="debugging">
          调试 API
        </el-button>
        <el-button @click="testAllMethods" type="success" :loading="testing">
          测试所有方法
        </el-button>
        <el-button @click="clearResults" type="info">
          清空结果
        </el-button>
      </div>
      
      <div class="debug-results" v-if="debugInfo">
        <h4>调试结果：</h4>
        <pre>{{ JSON.stringify(debugInfo, null, 2) }}</pre>
      </div>
      
      <div class="test-results" v-if="testResults.length > 0">
        <h4>测试结果：</h4>
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          class="result-item"
          :class="{ 'success': result.success, 'error': !result.success }"
        >
          <span>{{ result.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { 
  initHiprint as initHiprintPlugin, 
  createPrintTemplate, 
  detectHiprintAPI,
  safeAddElement,
  elementTemplates
} from '@/utils/hiprint-config';

const debugging = ref(false);
const testing = ref(false);
const debugInfo = ref(null);
const testResults = ref([]);

const addTestResult = (success, message) => {
  testResults.value.push({
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  });
};

const debugAPI = async () => {
  debugging.value = true;
  debugInfo.value = null;
  
  try {
    // 初始化 hiprint
    await initHiprintPlugin();
    
    // 获取 hiprint 实例
    const { hiprint } = await import('vue-plugin-hiprint');
    
    // 创建模板实例
    const template = new hiprint.PrintTemplate();
    
    // 收集调试信息
    const info = {
      hiprintVersion: hiprint.version || 'unknown',
      hiprintMethods: Object.getOwnPropertyNames(hiprint),
      templateMethods: Object.getOwnPropertyNames(template),
      templatePrototype: Object.getOwnPropertyNames(Object.getPrototypeOf(template)),
      templateConstructor: template.constructor.name,
      hasAddPrintElement: typeof template.addPrintElement === 'function',
      hasAddElement: typeof template.addElement === 'function',
      hasAddPrintPanel: typeof template.addPrintPanel === 'function',
      hasDesign: typeof template.design === 'function',
      hasGetJson: typeof template.getJson === 'function',
      hasPrint: typeof template.print === 'function',
      panels: template.panels,
      panelsType: Array.isArray(template.panels) ? 'array' : typeof template.panels,
      panelsLength: template.panels ? template.panels.length : 0
    };
    
    // 尝试创建面板
    try {
      if (typeof template.addPrintPanel === 'function') {
        const panel = template.addPrintPanel();
        info.panelCreated = true;
        info.panelMethods = panel ? Object.getOwnPropertyNames(panel) : null;
        info.panelPrototype = panel ? Object.getOwnPropertyNames(Object.getPrototypeOf(panel)) : null;
        info.panelHasAddPrintElement = panel ? typeof panel.addPrintElement === 'function' : false;
      }
    } catch (error) {
      info.panelCreationError = error.message;
    }
    
    // 使用我们的 API 检测
    const apiDetection = detectHiprintAPI();
    info.apiDetection = apiDetection;
    
    debugInfo.value = info;
    EleMessage.success('API 调试完成');
    
  } catch (error) {
    debugInfo.value = { error: error.message };
    EleMessage.error('API 调试失败：' + error.message);
  } finally {
    debugging.value = false;
  }
};

const testAllMethods = async () => {
  testing.value = true;
  testResults.value = [];
  
  try {
    addTestResult(true, '开始测试所有方法...');
    
    // 初始化
    await initHiprintPlugin();
    addTestResult(true, '✅ hiprint 初始化成功');
    
    // 创建模板
    const template = createPrintTemplate();
    addTestResult(true, '✅ 模板创建成功');
    
    // 测试不同的添加元素方法
    const testElement = {
      ...elementTemplates.text,
      options: {
        ...elementTemplates.text.options,
        left: 10,
        top: 10,
        title: '测试元素'
      }
    };
    
    // 方法1: 直接使用 addPrintElement
    try {
      if (typeof template.addPrintElement === 'function') {
        template.addPrintElement(testElement);
        addTestResult(true, '✅ template.addPrintElement 方法可用');
      } else {
        addTestResult(false, '❌ template.addPrintElement 方法不存在');
      }
    } catch (error) {
      addTestResult(false, `❌ template.addPrintElement 执行失败: ${error.message}`);
    }
    
    // 方法2: 使用 addElement
    try {
      if (typeof template.addElement === 'function') {
        template.addElement(testElement);
        addTestResult(true, '✅ template.addElement 方法可用');
      } else {
        addTestResult(false, '❌ template.addElement 方法不存在');
      }
    } catch (error) {
      addTestResult(false, `❌ template.addElement 执行失败: ${error.message}`);
    }
    
    // 方法3: 通过面板添加
    try {
      if (typeof template.addPrintPanel === 'function') {
        const panel = template.addPrintPanel();
        if (panel && typeof panel.addPrintElement === 'function') {
          panel.addPrintElement(testElement);
          addTestResult(true, '✅ panel.addPrintElement 方法可用');
        } else {
          addTestResult(false, '❌ panel.addPrintElement 方法不存在');
        }
      } else {
        addTestResult(false, '❌ template.addPrintPanel 方法不存在');
      }
    } catch (error) {
      addTestResult(false, `❌ 面板方法执行失败: ${error.message}`);
    }
    
    // 测试安全添加方法
    const safeResult = safeAddElement(template, testElement);
    if (safeResult.success) {
      addTestResult(true, `✅ safeAddElement 成功，使用方法: ${safeResult.method}`);
    } else {
      addTestResult(false, `❌ safeAddElement 失败: ${safeResult.error}`);
    }
    
    // 测试获取模板数据
    try {
      const templateData = template.getJson();
      if (templateData) {
        addTestResult(true, '✅ template.getJson 方法可用');
      } else {
        addTestResult(false, '❌ template.getJson 返回空数据');
      }
    } catch (error) {
      addTestResult(false, `❌ template.getJson 执行失败: ${error.message}`);
    }
    
    addTestResult(true, '🎉 所有方法测试完成！');
    EleMessage.success('方法测试完成');
    
  } catch (error) {
    addTestResult(false, `❌ 测试过程中发生错误: ${error.message}`);
    EleMessage.error('方法测试失败');
  } finally {
    testing.value = false;
  }
};

const clearResults = () => {
  debugInfo.value = null;
  testResults.value = [];
};
</script>

<style scoped>
.debug-api {
  padding: 20px;
}

.debug-actions {
  margin-bottom: 20px;
}

.debug-actions .el-button {
  margin-right: 10px;
}

.debug-results,
.test-results {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.debug-results pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
  font-size: 12px;
}

.result-item {
  padding: 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.result-item.success {
  background: #f0f9ff;
  color: #059669;
}

.result-item.error {
  background: #fef2f2;
  color: #dc2626;
}
</style>
