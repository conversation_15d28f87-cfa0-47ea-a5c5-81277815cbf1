<template>
  <div class="debug-api-page">
    <el-card class="full-width">
      <template #header>
        <div class="header-content">
          <span class="title">Hiprint 拖拽调试</span>
          <div class="actions">
            <el-button type="primary" @click="initDesigner">初始化设计器</el-button>
            <el-button type="success" @click="addTestElement">添加测试元素</el-button>
            <el-button type="warning" @click="refreshDesigner">刷新设计器</el-button>
            <el-button type="danger" @click="resetDesigner">重置设计器</el-button>
          </div>
        </div>
      </template>

      <div class="debug-content">
        <div class="left-panel">
          <div class="debug-panel">
            <h4>元素列表</h4>
            <div class="element-items">
              <div 
                v-for="(element, index) in basicElements" 
                :key="index" 
                class="element-item"
                draggable="true"
                @dragstart="handleDragStart($event, element)"
                @dragend="handleDragEnd"
              >
                {{ element.title }}
              </div>
            </div>
            
            <h4>测试操作</h4>
            <div class="test-actions">
              <el-button size="small" @click="addRandomElement">添加随机元素</el-button>
              <el-button size="small" @click="checkElements">检查元素</el-button>
              <el-button size="small" @click="inspectPanels">检查面板</el-button>
              <el-button size="small" @click="forceRefresh">强制刷新</el-button>
            </div>
            
            <div class="debug-logs">
              <h4>调试日志</h4>
              <div class="log-container">
                <div 
                  v-for="(log, index) in logs" 
                  :key="index"
                  class="log-item"
                  :class="log.type"
                >
                  <span class="log-time">{{ log.time }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
              <el-button size="small" @click="clearLogs">清空日志</el-button>
            </div>
          </div>
        </div>
        
        <div class="right-panel">
          <div 
            id="hiprint-container" 
            class="hiprint-container"
            @dragover="handleDragOver"
            @drop="handleDrop"
          >
            <!-- 设计器容器 -->
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { 
  initHiprint as initHiprintPlugin,
  createPrintTemplate,
  safeAddElement,
  elementTemplates
} from '@/utils/hiprint-config';

// 状态变量
const logs = ref([]);
const hiprintTemplate = ref(null);
const isInitialized = ref(false);

// 基本元素配置
const basicElements = [
  {
    tid: 'text',
    title: '文本',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 30,
      width: 200,
      text: '文本内容',
      fontSize: 14,
      fontFamily: 'Microsoft YaHei'
    }
  },
  {
    tid: 'rect',
    title: '矩形',
    type: 'rect',
    options: {
      left: 50,
      top: 100,
      height: 80,
      width: 120,
      borderWidth: 1,
      borderStyle: 'solid'
    }
  },
  {
    tid: 'line',
    title: '线条',
    type: 'line',
    options: {
      left: 50,
      top: 200,
      height: 1,
      width: 200
    }
  },
  {
    tid: 'field',
    title: '字段',
    type: 'text',
    options: {
      left: 50,
      top: 250,
      height: 30,
      width: 150,
      field: 'testField',
      text: '{{testField}}'
    }
  }
];

// 日志功能
const addLog = (type, message) => {
  logs.value.unshift({
    type,
    message,
    time: new Date().toLocaleTimeString()
  });
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const clearLogs = () => {
  logs.value = [];
};

// 设计器功能
const initDesigner = async () => {
  try {
    addLog('info', '初始化设计器...');
    
    // 初始化hiprint
    await initHiprintPlugin();
    
    // 创建模板
    hiprintTemplate.value = createPrintTemplate();
    addLog('success', '模板创建成功');
    
    // 添加面板
    if (typeof hiprintTemplate.value.addPrintPanel === 'function') {
      const panel = hiprintTemplate.value.addPrintPanel();
      addLog('success', '面板创建成功');
      if (panel) {
        addLog('info', `面板ID: ${panel.templateId}`);
      }
    }
    
    // 设置设计器
    const container = document.getElementById('hiprint-container');
    if (container && typeof hiprintTemplate.value.design === 'function') {
      hiprintTemplate.value.design(container, {
        grid: true,
        gridColor: '#ddd',
        showRuler: true
      });
      addLog('success', '设计器渲染成功');
    }
    
    isInitialized.value = true;
  } catch (error) {
    addLog('error', `设计器初始化失败: ${error.message}`);
    EleMessage.error('初始化失败: ' + error.message);
  }
};

const addTestElement = () => {
  if (!hiprintTemplate.value) {
    addLog('error', '请先初始化设计器');
    EleMessage.error('请先初始化设计器');
    return;
  }
  
  try {
    addLog('info', '添加测试文本元素...');
    
    // 创建文本元素
    const element = {
      tid: 'test_text_' + Date.now(),
      type: 'text',
      options: {
        left: 100,
        top: 100,
        width: 150,
        height: 40,
        text: '测试元素 ' + new Date().toLocaleTimeString(),
        backgroundColor: '#f0f0f0',
        borderWidth: 1,
        borderStyle: 'solid',
        borderColor: '#000000'
      }
    };
    
    // 添加元素
    const result = safeAddElement(hiprintTemplate.value, element);
    
    if (result.success) {
      addLog('success', `元素添加成功，使用方法: ${result.method}`);
      EleMessage.success('元素添加成功');
      
      // 检查元素
      setTimeout(() => {
        checkElements();
      }, 500);
    } else {
      addLog('error', `元素添加失败: ${result.error}`);
      EleMessage.error('元素添加失败');
    }
  } catch (error) {
    addLog('error', `添加元素出错: ${error.message}`);
    EleMessage.error('添加元素失败: ' + error.message);
  }
};

const refreshDesigner = () => {
  if (!hiprintTemplate.value) {
    addLog('error', '请先初始化设计器');
    return;
  }
  
  try {
    addLog('info', '刷新设计器...');
    
    if (typeof hiprintTemplate.value.refresh === 'function') {
      hiprintTemplate.value.refresh();
      addLog('success', '设计器刷新成功');
    } else {
      addLog('warn', '设计器没有refresh方法');
    }
  } catch (error) {
    addLog('error', `刷新设计器出错: ${error.message}`);
  }
};

const resetDesigner = () => {
  try {
    addLog('info', '重置设计器...');
    
    const container = document.getElementById('hiprint-container');
    if (container) {
      container.innerHTML = '';
    }
    
    hiprintTemplate.value = null;
    isInitialized.value = false;
    
    addLog('success', '设计器已重置');
    EleMessage.success('设计器已重置');
  } catch (error) {
    addLog('error', `重置设计器出错: ${error.message}`);
  }
};

// 拖拽功能
const handleDragStart = (event, element) => {
  try {
    addLog('info', `开始拖拽元素: ${element.title}`);
    
    const dragData = {
      type: 'basic',
      template: JSON.parse(JSON.stringify(element))
    };
    
    event.dataTransfer.setData('text/plain', JSON.stringify(dragData));
    event.dataTransfer.effectAllowed = 'copy';
  } catch (error) {
    addLog('error', `拖拽开始出错: ${error.message}`);
  }
};

const handleDragEnd = () => {
  addLog('info', '拖拽结束');
};

const handleDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = 'copy';
};

const handleDrop = (event) => {
  event.preventDefault();
  
  try {
    const dragDataStr = event.dataTransfer.getData('text/plain');
    if (!dragDataStr) {
      addLog('warn', '未获取到拖拽数据');
      return;
    }
    
    addLog('info', '接收到拖拽数据');
    
    if (!hiprintTemplate.value) {
      addLog('error', '请先初始化设计器');
      EleMessage.error('请先初始化设计器');
      return;
    }
    
    const dragData = JSON.parse(dragDataStr);
    
    // 获取拖拽位置
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    let element = dragData.template;
    element.options.left = x;
    element.options.top = y;
    
    // 确保可见
    element.options.backgroundColor = '#f0f0f0';
    element.options.borderWidth = 1;
    element.options.borderStyle = 'solid';
    element.options.borderColor = '#000000';
    
    // 添加元素
    const result = safeAddElement(hiprintTemplate.value, element);
    
    if (result.success) {
      addLog('success', `拖拽元素添加成功，使用方法: ${result.method}`);
      
      // 自动检查元素
      setTimeout(() => {
        checkElements();
      }, 500);
    } else {
      addLog('error', `拖拽元素添加失败: ${result.error}`);
    }
  } catch (error) {
    addLog('error', `处理拖拽放置出错: ${error.message}`);
  }
};

// 测试功能
const addRandomElement = () => {
  if (!hiprintTemplate.value) {
    addLog('error', '请先初始化设计器');
    return;
  }
  
  try {
    // 随机选择一个元素类型
    const elementTypes = ['text', 'rect', 'line', 'field'];
    const randomType = elementTypes[Math.floor(Math.random() * elementTypes.length)];
    
    // 随机位置
    const x = 50 + Math.floor(Math.random() * 300);
    const y = 50 + Math.floor(Math.random() * 200);
    
    let element;
    
    switch (randomType) {
      case 'text':
        element = {
          tid: 'random_text_' + Date.now(),
          type: 'text',
          options: {
            left: x,
            top: y,
            width: 150,
            height: 30,
            text: '随机文本 ' + Date.now(),
            backgroundColor: '#' + Math.floor(Math.random()*16777215).toString(16),
            fontSize: 14 + Math.floor(Math.random() * 10)
          }
        };
        break;
        
      case 'rect':
        element = {
          tid: 'random_rect_' + Date.now(),
          type: 'rect',
          options: {
            left: x,
            top: y,
            width: 50 + Math.floor(Math.random() * 100),
            height: 50 + Math.floor(Math.random() * 100),
            borderWidth: 1 + Math.floor(Math.random() * 3),
            backgroundColor: '#' + Math.floor(Math.random()*16777215).toString(16)
          }
        };
        break;
        
      case 'line':
        element = {
          tid: 'random_line_' + Date.now(),
          type: 'line',
          options: {
            left: x,
            top: y,
            width: 100 + Math.floor(Math.random() * 150),
            height: 1,
            borderWidth: 1 + Math.floor(Math.random() * 5)
          }
        };
        break;
        
      case 'field':
        element = {
          tid: 'random_field_' + Date.now(),
          type: 'text',
          options: {
            left: x,
            top: y,
            width: 120,
            height: 30,
            field: 'field_' + Date.now(),
            title: '字段 ' + Date.now(),
            backgroundColor: '#' + Math.floor(Math.random()*16777215).toString(16)
          }
        };
        break;
    }
    
    addLog('info', `添加随机元素: ${randomType}`);
    
    // 添加元素
    const result = safeAddElement(hiprintTemplate.value, element);
    
    if (result.success) {
      addLog('success', `随机元素添加成功，使用方法: ${result.method}`);
    } else {
      addLog('error', `随机元素添加失败: ${result.error}`);
    }
  } catch (error) {
    addLog('error', `添加随机元素出错: ${error.message}`);
  }
};

const checkElements = () => {
  if (!hiprintTemplate.value) {
    addLog('error', '请先初始化设计器');
    return;
  }
  
  try {
    addLog('info', '检查元素...');
    
    // 检查是否有面板
    const panels = hiprintTemplate.value.panels || [];
    
    addLog('info', `设计器面板数量: ${panels.length}`);
    
    if (panels.length === 0) {
      addLog('warn', '设计器没有面板');
      return;
    }
    
    // 检查每个面板的元素
    panels.forEach((panel, index) => {
      const elements = panel.printElements || panel.elements || [];
      const elementCount = Array.isArray(elements) ? elements.length : 0;
      
      addLog('info', `面板 ${index + 1} 元素数量: ${elementCount}`);
      
      if (elementCount > 0) {
        elements.forEach((element, elementIndex) => {
          const elementType = element.type || element.printElementType?.type || '未知';
          const elementId = element.tid || element.id || `元素${elementIndex + 1}`;
          addLog('success', `- 元素: ${elementId} (类型: ${elementType})`);
        });
      } else {
        addLog('warn', `面板 ${index + 1} 没有元素`);
      }
    });
  } catch (error) {
    addLog('error', `检查元素出错: ${error.message}`);
  }
};

const inspectPanels = () => {
  if (!hiprintTemplate.value) {
    addLog('error', '请先初始化设计器');
    return;
  }
  
  try {
    addLog('info', '检查面板结构...');
    
    // 检查模板对象
    const templateKeys = Object.keys(hiprintTemplate.value);
    addLog('info', `模板对象属性: ${templateKeys.join(', ')}`);
    
    // 检查面板数组
    const panels = hiprintTemplate.value.panels || [];
    addLog('info', `面板数量: ${panels.length}`);
    
    panels.forEach((panel, index) => {
      const panelId = panel.templateId || `面板${index}`;
      addLog('info', `面板 ${index + 1} ID: ${panelId}`);
      
      // 列出面板的主要方法
      const panelMethods = [];
      ['addPrintElement', 'addPrintText', 'addPrintImage', 'updatePrintElements'].forEach(method => {
        if (typeof panel[method] === 'function') {
          panelMethods.push(method);
        }
      });
      
      addLog('info', `面板 ${index + 1} 可用方法: ${panelMethods.join(', ')}`);
      
      // 检查DOM元素
      const container = document.getElementById('hiprint-container');
      if (container) {
        const panelElement = container.querySelector('.hiprint-printPanel');
        if (panelElement) {
          addLog('success', '找到面板DOM元素');
          
          // 检查元素DOM
          const elementDoms = panelElement.querySelectorAll('.hiprint-printElement');
          addLog('info', `DOM中的元素数量: ${elementDoms.length}`);
          
          if (elementDoms.length > 0) {
            addLog('success', 'DOM中存在元素');
          } else {
            addLog('warn', 'DOM中没有元素');
          }
        } else {
          addLog('warn', '未找到面板DOM元素');
        }
      }
    });
  } catch (error) {
    addLog('error', `检查面板出错: ${error.message}`);
  }
};

const forceRefresh = () => {
  if (!hiprintTemplate.value) {
    addLog('error', '请先初始化设计器');
    return;
  }
  
  try {
    addLog('info', '强制刷新设计器...');
    
    // 获取当前设计器状态
    const currentJson = hiprintTemplate.value.getJson ? hiprintTemplate.value.getJson() : null;
    
    if (!currentJson) {
      addLog('warn', '无法获取当前设计器状态');
      return;
    }
    
    addLog('info', '当前设计器状态已保存');
    
    // 重置并重新渲染
    const container = document.getElementById('hiprint-container');
    if (container) {
      container.innerHTML = '';
      
      // 重新创建模板
      hiprintTemplate.value = createPrintTemplate(currentJson);
      
      // 重新设计
      hiprintTemplate.value.design(container, {
        grid: true,
        gridColor: '#ddd',
        showRuler: true
      });
      
      addLog('success', '设计器已强制刷新');
      EleMessage.success('设计器已强制刷新');
    }
  } catch (error) {
    addLog('error', `强制刷新出错: ${error.message}`);
  }
};

// 生命周期
onMounted(() => {
  addLog('info', '调试页面已加载');
});

onUnmounted(() => {
  if (hiprintTemplate.value) {
    try {
      hiprintTemplate.value.clear && hiprintTemplate.value.clear();
    } catch (e) {
      console.error('清理设计器时出错:', e);
    }
  }
});
</script>

<style scoped>
.debug-api-page {
  padding: 16px;
}

.full-width {
  width: 100%;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 16px;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 10px;
}

.debug-content {
  display: flex;
  gap: 16px;
  height: calc(100vh - 200px);
}

.left-panel {
  flex: 0 0 300px;
  overflow-y: auto;
  border-right: 1px solid #eee;
}

.right-panel {
  flex: 1;
  overflow: auto;
}

.debug-panel {
  padding: 10px;
}

.debug-panel h4 {
  margin: 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.element-items {
  margin-bottom: 16px;
}

.element-item {
  background: #f5f5f5;
  border: 1px solid #ddd;
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.2s;
}

.element-item:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.test-actions {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.debug-logs {
  margin-top: 16px;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.log-container {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  padding: 8px;
  margin-bottom: 8px;
  background: #f9f9f9;
  border-radius: 4px;
  font-family: monospace;
}

.log-item {
  margin-bottom: 2px;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 1.4;
  display: flex;
  gap: 8px;
}

.log-item.info {
  background: #e6f7ff;
  color: #1890ff;
}

.log-item.success {
  background: #f6ffed;
  color: #52c41a;
}

.log-item.warn {
  background: #fffbe6;
  color: #faad14;
}

.log-item.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.log-time {
  font-weight: bold;
  white-space: nowrap;
}

.hiprint-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  background: #ffffff;
  border: 1px dashed #cccccc;
}
</style>
