<template>
  <div class="element-debug">
    <el-card>
      <template #header>
        <span>元素添加调试工具</span>
      </template>
      
      <div class="debug-actions">
        <el-button @click="initDebug" type="primary" :loading="initializing">
          初始化调试
        </el-button>
        <el-button @click="addTestElement" type="success" :disabled="!template">
          添加测试元素
        </el-button>
        <el-button @click="checkTemplateState" type="info" :disabled="!template">
          检查模板状态
        </el-button>
        <el-button @click="forceRefresh" type="warning" :disabled="!template">
          强制刷新
        </el-button>
        <el-button @click="clearDebug" type="danger">
          清空调试
        </el-button>
      </div>
      
      <div class="debug-info" v-if="debugInfo">
        <h4>调试信息：</h4>
        <div class="info-grid">
          <div class="info-item">
            <strong>模板状态:</strong> {{ template ? '已创建' : '未创建' }}
          </div>
          <div class="info-item">
            <strong>面板数量:</strong> {{ debugInfo.panelCount }}
          </div>
          <div class="info-item">
            <strong>总元素数量:</strong> {{ debugInfo.totalElements }}
          </div>
          <div class="info-item">
            <strong>设计器状态:</strong> {{ debugInfo.designerActive ? '活跃' : '未激活' }}
          </div>
        </div>
        
        <div class="panel-details" v-if="debugInfo.panels && debugInfo.panels.length > 0">
          <h5>面板详情：</h5>
          <div v-for="(panel, index) in debugInfo.panels" :key="index" class="panel-item">
            <strong>面板 {{ index + 1 }}:</strong>
            <ul>
              <li>元素数量: {{ panel.elementCount }}</li>
              <li>面板ID: {{ panel.id || 'unknown' }}</li>
              <li>面板类型: {{ panel.type || 'unknown' }}</li>
              <li>可见性: {{ panel.visible !== false ? '可见' : '隐藏' }}</li>
            </ul>
            <div v-if="panel.elements && panel.elements.length > 0" class="element-list">
              <strong>元素列表:</strong>
              <div v-for="(element, eIndex) in panel.elements" :key="eIndex" class="element-item">
                <span>{{ eIndex + 1 }}. {{ element.type || element.tid || 'unknown' }}</span>
                <span class="element-pos">({{ element.left || 0 }}, {{ element.top || 0 }})</span>
                <span class="element-size">{{ element.width || 0 }}x{{ element.height || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="debug-container" id="element-debug-container" style="height: 400px; border: 2px solid #409eff; margin-top: 20px; position: relative;">
        <div class="container-label">设计器容器</div>
        <!-- 设计器容器 -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { 
  initHiprint as initHiprintPlugin, 
  createPrintTemplate, 
  designerConfig,
  safeAddElement,
  elementTemplates
} from '@/utils/hiprint-config';

const initializing = ref(false);
const template = ref(null);
const debugInfo = ref(null);

const initDebug = async () => {
  initializing.value = true;
  
  try {
    // 初始化 hiprint
    await initHiprintPlugin();
    console.log('✅ hiprint 初始化成功');
    
    // 创建模板
    template.value = createPrintTemplate();
    console.log('✅ 模板创建成功:', template.value);
    
    // 等待 DOM 更新
    await nextTick();
    
    // 显示设计器
    const container = document.getElementById('element-debug-container');
    if (container && typeof template.value.design === 'function') {
      container.innerHTML = '<div class="container-label">设计器容器</div>';
      
      // 使用更详细的配置
      const config = {
        ...designerConfig,
        moveable: true,
        resizable: true,
        showGrid: true,
        gridSize: 10,
        autoWidth: true,
        autoHeight: true
      };
      
      template.value.design(container, config);
      console.log('✅ 设计器渲染成功');
    }
    
    // 更新调试信息
    updateDebugInfo();
    
    EleMessage.success('调试初始化完成');
    
  } catch (error) {
    console.error('❌ 初始化失败:', error);
    EleMessage.error('初始化失败：' + error.message);
  } finally {
    initializing.value = false;
  }
};

const addTestElement = () => {
  if (!template.value) {
    EleMessage.error('请先初始化调试');
    return;
  }
  
  // 创建一个更明显的测试元素
  const testElement = {
    tid: 'test-text-' + Date.now(),
    title: '调试文本',
    type: 'text',
    options: {
      left: 100,
      top: 100,
      height: 40,
      width: 250,
      title: '调试文本元素',
      text: '这是一个测试文本元素 ' + new Date().toLocaleTimeString(),
      fontSize: 16,
      fontFamily: 'Microsoft YaHei',
      color: '#ff0000',  // 红色文字
      backgroundColor: '#ffff00',  // 黄色背景
      textAlign: 'center',
      lineHeight: 1.5,
      borderWidth: 2,
      borderStyle: 'solid',
      borderColor: '#0000ff'  // 蓝色边框
    }
  };
  
  console.log('准备添加测试元素:', testElement);
  
  const result = safeAddElement(template.value, testElement);
  
  if (result.success) {
    console.log('✅ 元素添加成功:', result);
    EleMessage.success(`元素添加成功，使用方法: ${result.method}`);
    
    // 等待一下再更新调试信息
    setTimeout(() => {
      updateDebugInfo();
      forceRefresh();
    }, 100);
  } else {
    console.error('❌ 元素添加失败:', result);
    EleMessage.error('元素添加失败: ' + result.error);
  }
};

const checkTemplateState = () => {
  if (!template.value) {
    EleMessage.error('模板未初始化');
    return;
  }
  
  console.log('=== 模板状态检查 ===');
  console.log('模板对象:', template.value);
  console.log('模板方法:', Object.getOwnPropertyNames(template.value));
  console.log('面板数组:', template.value.panels);
  
  if (template.value.panels && template.value.panels.length > 0) {
    template.value.panels.forEach((panel, index) => {
      console.log(`面板 ${index}:`, panel);
      console.log(`面板 ${index} 方法:`, Object.getOwnPropertyNames(panel));
      console.log(`面板 ${index} 元素:`, panel.printElements || panel.elements || 'unknown');
    });
  }
  
  // 尝试获取模板数据
  try {
    const templateData = template.value.getJson();
    console.log('模板JSON数据:', templateData);
  } catch (error) {
    console.error('获取模板数据失败:', error);
  }
  
  updateDebugInfo();
  EleMessage.info('模板状态已输出到控制台');
};

const forceRefresh = () => {
  if (!template.value) return;
  
  try {
    // 尝试多种刷新方法
    if (typeof template.value.refresh === 'function') {
      template.value.refresh();
      console.log('✅ 使用 template.refresh()');
    }
    
    if (typeof template.value.update === 'function') {
      template.value.update();
      console.log('✅ 使用 template.update()');
    }
    
    if (typeof template.value.render === 'function') {
      template.value.render();
      console.log('✅ 使用 template.render()');
    }
    
    // 尝试重新渲染设计器
    const container = document.getElementById('element-debug-container');
    if (container) {
      // 清空容器并重新渲染
      container.innerHTML = '<div class="container-label">设计器容器</div>';
      template.value.design(container, designerConfig);
      console.log('✅ 重新渲染设计器');
    }
    
    EleMessage.success('强制刷新完成');
  } catch (error) {
    console.error('强制刷新失败:', error);
    EleMessage.error('强制刷新失败: ' + error.message);
  }
};

const updateDebugInfo = () => {
  if (!template.value) {
    debugInfo.value = null;
    return;
  }
  
  const info = {
    panelCount: template.value.panels ? template.value.panels.length : 0,
    totalElements: 0,
    designerActive: !!document.querySelector('#element-debug-container .hiprint-printTemplate'),
    panels: []
  };
  
  if (template.value.panels && template.value.panels.length > 0) {
    template.value.panels.forEach((panel, index) => {
      const elements = panel.printElements || panel.elements || [];
      const panelInfo = {
        id: panel.id || `panel-${index}`,
        type: panel.type || 'unknown',
        elementCount: Array.isArray(elements) ? elements.length : 0,
        visible: panel.visible !== false,
        elements: Array.isArray(elements) ? elements.map(el => ({
          type: el.type || el.tid || 'unknown',
          left: el.options?.left || el.left || 0,
          top: el.options?.top || el.top || 0,
          width: el.options?.width || el.width || 0,
          height: el.options?.height || el.height || 0
        })) : []
      };
      
      info.panels.push(panelInfo);
      info.totalElements += panelInfo.elementCount;
    });
  }
  
  debugInfo.value = info;
  console.log('调试信息更新:', info);
};

const clearDebug = () => {
  template.value = null;
  debugInfo.value = null;
  
  const container = document.getElementById('element-debug-container');
  if (container) {
    container.innerHTML = '<div class="container-label">设计器容器</div>';
  }
  
  EleMessage.info('调试已清空');
};
</script>

<style scoped>
.element-debug {
  padding: 20px;
}

.debug-actions {
  margin-bottom: 20px;
}

.debug-actions .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.debug-info {
  margin: 20px 0;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 4px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 16px;
}

.info-item {
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.panel-details {
  margin-top: 16px;
}

.panel-item {
  margin-bottom: 16px;
  padding: 12px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.panel-item ul {
  margin: 8px 0;
  padding-left: 20px;
}

.element-list {
  margin-top: 8px;
}

.element-item {
  display: flex;
  gap: 10px;
  padding: 4px 0;
  font-family: monospace;
  font-size: 12px;
}

.element-pos {
  color: #666;
}

.element-size {
  color: #999;
}

#element-debug-container {
  background: #fff;
  border-radius: 4px;
  position: relative;
}

.container-label {
  position: absolute;
  top: 5px;
  left: 5px;
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 12px;
  z-index: 1000;
}
</style>
