<template>
  <div class="quick-test">
    <el-card>
      <template #header>
        <span>快速测试 - 修复验证</span>
      </template>
      
      <div class="test-buttons">
        <el-button @click="testBasicFunction" type="primary" :loading="testing">
          测试基础功能
        </el-button>
        <el-button @click="testDesigner" type="success" :loading="designerTesting">
          测试设计器
        </el-button>
        <el-button @click="clearResults" type="info">
          清空结果
        </el-button>
      </div>
      
      <div class="test-results" v-if="results.length > 0">
        <h4>测试结果：</h4>
        <div 
          v-for="(result, index) in results" 
          :key="index"
          class="result-item"
          :class="{ 'success': result.success, 'error': !result.success }"
        >
          <el-icon>
            <Check v-if="result.success" />
            <Close v-else />
          </el-icon>
          <span>{{ result.message }}</span>
        </div>
      </div>
      
      <div id="test-designer-container" style="height: 300px; border: 1px solid #ddd; margin-top: 20px;">
        <!-- 设计器测试容器 -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Check, Close } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';
import { 
  initHiprint as initHiprintPlugin, 
  createPrintTemplate, 
  designerConfig, 
  handleHiprintError,
  elementTemplates 
} from '@/utils/hiprint-config';

const testing = ref(false);
const designerTesting = ref(false);
const results = ref([]);

const addResult = (success, message) => {
  results.value.push({
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  });
};

const testBasicFunction = async () => {
  testing.value = true;
  results.value = [];
  
  try {
    addResult(true, '开始基础功能测试...');
    
    // 测试 1: 初始化 hiprint
    try {
      await initHiprintPlugin();
      addResult(true, '✅ hiprint 插件初始化成功');
    } catch (error) {
      addResult(false, '❌ hiprint 插件初始化失败: ' + error.message);
      return;
    }
    
    // 测试 2: 创建模板
    try {
      const template = createPrintTemplate();
      addResult(true, '✅ 模板创建成功');
    } catch (error) {
      addResult(false, '❌ 模板创建失败: ' + error.message);
      return;
    }
    
    // 测试 3: 检查元素模板
    try {
      const textElement = elementTemplates.text;
      const barcodeElement = elementTemplates.barcode;
      if (textElement && barcodeElement) {
        addResult(true, '✅ 元素模板定义正确');
      } else {
        addResult(false, '❌ 元素模板定义缺失');
      }
    } catch (error) {
      addResult(false, '❌ 元素模板检查失败: ' + error.message);
    }
    
    // 测试 4: 错误处理
    try {
      const testError = new Error('WebSocket connection failed');
      const errorMessage = handleHiprintError(testError);
      addResult(true, '✅ 错误处理功能正常: ' + errorMessage);
    } catch (error) {
      addResult(false, '❌ 错误处理功能异常: ' + error.message);
    }
    
    addResult(true, '🎉 基础功能测试完成！');
    EleMessage.success('基础功能测试通过');
    
  } catch (error) {
    addResult(false, '❌ 测试过程中发生错误: ' + error.message);
    EleMessage.error('基础功能测试失败');
  } finally {
    testing.value = false;
  }
};

const testDesigner = async () => {
  designerTesting.value = true;
  
  try {
    addResult(true, '开始设计器测试...');
    
    // 初始化 hiprint
    await initHiprintPlugin();
    addResult(true, '✅ hiprint 插件初始化成功');
    
    // 创建模板
    const template = createPrintTemplate();
    addResult(true, '✅ 模板实例创建成功');
    
    // 添加测试元素
    template.addPrintElement({
      ...elementTemplates.text,
      options: {
        ...elementTemplates.text.options,
        left: 50,
        top: 50,
        title: '测试文本元素'
      }
    });
    
    template.addPrintElement({
      ...elementTemplates.barcode,
      options: {
        ...elementTemplates.barcode.options,
        left: 50,
        top: 100,
        value: '123456789'
      }
    });
    
    addResult(true, '✅ 元素添加成功');
    
    // 在容器中显示设计器
    const container = document.getElementById('test-designer-container');
    if (container) {
      container.innerHTML = '';
      template.design(container, designerConfig);
      addResult(true, '✅ 设计器渲染成功');
    } else {
      addResult(false, '❌ 设计器容器未找到');
    }
    
    // 测试获取模板数据
    const templateData = template.getJson();
    if (templateData && templateData.panels) {
      addResult(true, '✅ 模板数据获取成功');
    } else {
      addResult(false, '❌ 模板数据获取失败');
    }
    
    addResult(true, '🎉 设计器测试完成！');
    EleMessage.success('设计器测试通过');
    
  } catch (error) {
    const errorMessage = handleHiprintError(error);
    addResult(false, '❌ 设计器测试失败: ' + errorMessage);
    EleMessage.error('设计器测试失败');
  } finally {
    designerTesting.value = false;
  }
};

const clearResults = () => {
  results.value = [];
  const container = document.getElementById('test-designer-container');
  if (container) {
    container.innerHTML = '';
  }
};
</script>

<style scoped>
.quick-test {
  padding: 20px;
}

.test-buttons {
  margin-bottom: 20px;
}

.test-buttons .el-button {
  margin-right: 10px;
}

.test-results {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
}

.result-item.success {
  background: #f0f9ff;
  color: #059669;
}

.result-item.error {
  background: #fef2f2;
  color: #dc2626;
}

.result-item .el-icon {
  margin-right: 8px;
}

#test-designer-container {
  background: #fff;
  border-radius: 4px;
}
</style>
