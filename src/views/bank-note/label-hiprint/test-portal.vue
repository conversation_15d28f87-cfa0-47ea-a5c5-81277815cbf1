<template>
  <div class="test-portal">
    <el-card>
      <template #header>
        <div class="portal-header">
          <h2>🧪 hiprint 测试工具门户</h2>
          <p>选择一个测试工具来诊断和调试 hiprint 功能</p>
        </div>
      </template>
      
      <div class="test-grid">
        <div 
          v-for="tool in testTools" 
          :key="tool.id"
          class="test-card"
          @click="openTool(tool)"
        >
          <div class="tool-icon">{{ tool.icon }}</div>
          <h3>{{ tool.name }}</h3>
          <p>{{ tool.description }}</p>
          <div class="tool-tags">
            <el-tag 
              v-for="tag in tool.tags" 
              :key="tag" 
              size="small"
              :type="getTagType(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <div class="portal-actions">
        <el-button @click="openAllTests" type="primary">
          打开所有测试
        </el-button>
        <el-button @click="runQuickDiagnosis" type="success" :loading="diagnosing">
          快速诊断
        </el-button>
      </div>
      
      <div class="diagnosis-result" v-if="diagnosisResult">
        <h4>快速诊断结果：</h4>
        <div class="result-grid">
          <div 
            v-for="(result, key) in diagnosisResult" 
            :key="key"
            class="result-item"
            :class="{ 'success': result.status === 'ok', 'error': result.status === 'error' }"
          >
            <strong>{{ result.name }}:</strong>
            <span>{{ result.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';

const diagnosing = ref(false);
const diagnosisResult = ref(null);

const testTools = [
  {
    id: 'simple-test',
    name: '简单测试',
    icon: '🔍',
    description: '逐步测试 hiprint 的基础功能，提供详细的执行日志',
    tags: ['基础', '调试', '推荐'],
    component: 'simple-test.vue'
  },
  {
    id: 'api-explorer',
    name: 'API 探索器',
    icon: '🔬',
    description: '深入分析 hiprint 对象结构和可用方法',
    tags: ['高级', 'API', '分析'],
    component: 'api-explorer.vue'
  },
  {
    id: 'element-debug',
    name: '元素调试器',
    icon: '🛠️',
    description: '专门调试元素添加和显示问题',
    tags: ['元素', '调试', '可视化'],
    component: 'element-debug.vue'
  },
  {
    id: 'element-test',
    name: '元素测试器',
    icon: '📝',
    description: '测试不同类型元素的添加功能',
    tags: ['元素', '测试', '交互'],
    component: 'element-test.vue'
  },
  {
    id: 'quick-test',
    name: '快速测试',
    icon: '⚡',
    description: '快速验证核心功能是否正常工作',
    tags: ['快速', '验证', '概览'],
    component: 'quick-test.vue'
  }
];

const getTagType = (tag) => {
  const tagTypes = {
    '基础': 'primary',
    '高级': 'warning',
    '推荐': 'success',
    '调试': 'info',
    '元素': 'primary',
    '测试': 'success',
    '快速': 'warning',
    'API': 'danger',
    '分析': 'info',
    '可视化': 'success',
    '交互': 'primary',
    '验证': 'success',
    '概览': 'info'
  };
  return tagTypes[tag] || '';
};

const openTool = (tool) => {
  // 方法1: 尝试在当前页面中动态加载组件
  try {
    const testWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes');
    
    testWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>${tool.name} - hiprint 测试工具</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body { 
            margin: 0; 
            padding: 20px; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
          }
          .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
          }
          .instructions {
            background: #f0f9ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
          }
          .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
          }
          .btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
          }
          .btn:hover { background: #337ecc; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${tool.icon} ${tool.name}</h1>
            <p>${tool.description}</p>
          </div>
          
          <div class="instructions">
            <h3>📋 使用说明</h3>
            <p>由于技术限制，无法直接在新窗口中加载 Vue 组件。请使用以下方法之一：</p>
            
            <h4>方法1: 在主项目中访问</h4>
            <p>在浏览器地址栏中输入以下地址：</p>
            <div class="code-block">
${window.location.origin}${window.location.pathname}#/${tool.id}
            </div>
            
            <h4>方法2: 在控制台中运行</h4>
            <p>打开浏览器开发者工具，在控制台中运行：</p>
            <div class="code-block">
// 导入并运行测试组件
import('${window.location.origin}/src/views/bank-note/label-hiprint/${tool.component}')
  .then(module => {
    console.log('测试组件已加载:', module);
  })
  .catch(error => {
    console.error('加载失败:', error);
  });
            </div>
            
            <h4>方法3: 直接在主页面中测试</h4>
            <p>返回主设计器页面，点击右上角的"测试工具"下拉菜单。</p>
          </div>
          
          <div style="text-align: center;">
            <button class="btn" onclick="window.close()">关闭窗口</button>
            <button class="btn" onclick="window.opener.focus(); window.close();">返回主页面</button>
          </div>
        </div>
      </body>
      </html>
    `);
    
    testWindow.document.close();
  } catch (error) {
    EleMessage.error('打开测试工具失败：' + error.message);
  }
};

const openAllTests = () => {
  testTools.forEach((tool, index) => {
    setTimeout(() => {
      openTool(tool);
    }, index * 500); // 延迟打开，避免浏览器阻止弹窗
  });
};

const runQuickDiagnosis = async () => {
  diagnosing.value = true;
  diagnosisResult.value = null;
  
  try {
    const results = {};
    
    // 检查1: hiprint 模块加载
    try {
      const hiprintModule = await import('vue-plugin-hiprint');
      results.hiprint = {
        name: 'hiprint 模块',
        status: 'ok',
        message: '✅ 模块加载成功'
      };
    } catch (error) {
      results.hiprint = {
        name: 'hiprint 模块',
        status: 'error',
        message: '❌ 模块加载失败: ' + error.message
      };
    }
    
    // 检查2: 初始化
    try {
      const { hiprint } = await import('vue-plugin-hiprint');
      hiprint.init({ host: '', token: '', debug: false });
      results.init = {
        name: '初始化',
        status: 'ok',
        message: '✅ 初始化成功'
      };
    } catch (error) {
      results.init = {
        name: '初始化',
        status: 'error',
        message: '❌ 初始化失败: ' + error.message
      };
    }
    
    // 检查3: 模板创建
    try {
      const { hiprint } = await import('vue-plugin-hiprint');
      const template = new hiprint.PrintTemplate();
      results.template = {
        name: '模板创建',
        status: 'ok',
        message: '✅ 模板创建成功'
      };
    } catch (error) {
      results.template = {
        name: '模板创建',
        status: 'error',
        message: '❌ 模板创建失败: ' + error.message
      };
    }
    
    // 检查4: 面板创建
    try {
      const { hiprint } = await import('vue-plugin-hiprint');
      const template = new hiprint.PrintTemplate();
      const panel = template.addPrintPanel();
      results.panel = {
        name: '面板创建',
        status: panel ? 'ok' : 'error',
        message: panel ? '✅ 面板创建成功' : '❌ 面板创建失败'
      };
    } catch (error) {
      results.panel = {
        name: '面板创建',
        status: 'error',
        message: '❌ 面板创建失败: ' + error.message
      };
    }
    
    diagnosisResult.value = results;
    
    const successCount = Object.values(results).filter(r => r.status === 'ok').length;
    const totalCount = Object.keys(results).length;
    
    if (successCount === totalCount) {
      EleMessage.success(`诊断完成：${successCount}/${totalCount} 项检查通过`);
    } else {
      EleMessage.warning(`诊断完成：${successCount}/${totalCount} 项检查通过`);
    }
    
  } catch (error) {
    EleMessage.error('诊断失败：' + error.message);
  } finally {
    diagnosing.value = false;
  }
};
</script>

<style scoped>
.test-portal {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.portal-header {
  text-align: center;
  margin-bottom: 20px;
}

.portal-header h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.portal-header p {
  margin: 0;
  color: #666;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.test-card {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.test-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.tool-icon {
  font-size: 32px;
  text-align: center;
  margin-bottom: 10px;
}

.test-card h3 {
  margin: 0 0 10px 0;
  color: #333;
  text-align: center;
}

.test-card p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.tool-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.portal-actions {
  text-align: center;
  margin-bottom: 30px;
}

.portal-actions .el-button {
  margin: 0 10px;
}

.diagnosis-result {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
  margin-top: 15px;
}

.result-item {
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
}

.result-item.success {
  background: #f0f9ff;
  color: #059669;
}

.result-item.error {
  background: #fef2f2;
  color: #dc2626;
}
</style>
