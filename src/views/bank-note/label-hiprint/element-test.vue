<template>
  <div class="element-test">
    <el-card>
      <template #header>
        <span>元素添加测试</span>
      </template>
      
      <div class="test-actions">
        <el-button @click="initTest" type="primary" :loading="initializing">
          初始化测试
        </el-button>
        <el-button @click="addTextElement" type="success" :disabled="!template">
          添加文本元素
        </el-button>
        <el-button @click="addImageElement" type="info" :disabled="!template">
          添加图片元素
        </el-button>
        <el-button @click="addLineElement" type="warning" :disabled="!template">
          添加线条元素
        </el-button>
        <el-button @click="clearTest" type="danger">
          清空测试
        </el-button>
      </div>
      
      <div class="test-info" v-if="testInfo">
        <h4>测试信息：</h4>
        <div class="info-item">
          <strong>模板状态:</strong> {{ template ? '已创建' : '未创建' }}
        </div>
        <div class="info-item">
          <strong>面板数量:</strong> {{ template && template.panels ? template.panels.length : 0 }}
        </div>
        <div class="info-item">
          <strong>最后操作:</strong> {{ lastOperation }}
        </div>
      </div>
      
      <div class="test-results" v-if="results.length > 0">
        <h4>操作结果：</h4>
        <div 
          v-for="(result, index) in results" 
          :key="index"
          class="result-item"
          :class="{ 'success': result.success, 'error': !result.success }"
        >
          <span>{{ result.message }}</span>
          <small v-if="result.method">（方法: {{ result.method }}）</small>
        </div>
      </div>
      
      <div class="test-container" id="element-test-container" style="height: 400px; border: 1px solid #ddd; margin-top: 20px;">
        <!-- 设计器容器 -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { 
  initHiprint as initHiprintPlugin, 
  createPrintTemplate, 
  designerConfig,
  safeAddElement,
  elementTemplates
} from '@/utils/hiprint-config';

const initializing = ref(false);
const template = ref(null);
const testInfo = ref(false);
const lastOperation = ref('无');
const results = ref([]);

const addResult = (success, message, method = null) => {
  results.value.push({
    success,
    message,
    method,
    timestamp: new Date().toLocaleTimeString()
  });
  lastOperation.value = message;
};

const initTest = async () => {
  initializing.value = true;
  results.value = [];
  
  try {
    addResult(true, '开始初始化测试...');
    
    // 初始化 hiprint
    await initHiprintPlugin();
    addResult(true, '✅ hiprint 插件初始化成功');
    
    // 创建模板
    template.value = createPrintTemplate();
    addResult(true, '✅ 模板创建成功');
    
    // 显示设计器
    const container = document.getElementById('element-test-container');
    if (container && typeof template.value.design === 'function') {
      container.innerHTML = '';
      template.value.design(container, designerConfig);
      addResult(true, '✅ 设计器渲染成功');
    } else {
      addResult(false, '❌ 设计器渲染失败');
    }
    
    testInfo.value = true;
    EleMessage.success('测试初始化完成');
    
  } catch (error) {
    addResult(false, '❌ 初始化失败: ' + error.message);
    EleMessage.error('初始化失败：' + error.message);
  } finally {
    initializing.value = false;
  }
};

const addTextElement = () => {
  if (!template.value) {
    EleMessage.error('请先初始化测试');
    return;
  }
  
  const textElement = {
    ...elementTemplates.text,
    options: {
      ...elementTemplates.text.options,
      left: 50 + Math.random() * 100,
      top: 50 + Math.random() * 100,
      text: '测试文本 ' + Date.now()
    }
  };
  
  const result = safeAddElement(template.value, textElement);
  if (result.success) {
    addResult(true, '✅ 文本元素添加成功', result.method);
    EleMessage.success('文本元素添加成功');
  } else {
    addResult(false, '❌ 文本元素添加失败: ' + result.error, null);
    EleMessage.error('文本元素添加失败');
    console.error('详细错误:', result);
  }
};

const addImageElement = () => {
  if (!template.value) {
    EleMessage.error('请先初始化测试');
    return;
  }
  
  const imageElement = {
    ...elementTemplates.image,
    options: {
      ...elementTemplates.image.options,
      left: 50 + Math.random() * 100,
      top: 50 + Math.random() * 100
    }
  };
  
  const result = safeAddElement(template.value, imageElement);
  if (result.success) {
    addResult(true, '✅ 图片元素添加成功', result.method);
    EleMessage.success('图片元素添加成功');
  } else {
    addResult(false, '❌ 图片元素添加失败: ' + result.error, null);
    EleMessage.error('图片元素添加失败');
    console.error('详细错误:', result);
  }
};

const addLineElement = () => {
  if (!template.value) {
    EleMessage.error('请先初始化测试');
    return;
  }
  
  const lineElement = {
    ...elementTemplates.line,
    options: {
      ...elementTemplates.line.options,
      left: 50 + Math.random() * 100,
      top: 50 + Math.random() * 100
    }
  };
  
  const result = safeAddElement(template.value, lineElement);
  if (result.success) {
    addResult(true, '✅ 线条元素添加成功', result.method);
    EleMessage.success('线条元素添加成功');
  } else {
    addResult(false, '❌ 线条元素添加失败: ' + result.error, null);
    EleMessage.error('线条元素添加失败');
    console.error('详细错误:', result);
  }
};

const clearTest = () => {
  template.value = null;
  testInfo.value = false;
  results.value = [];
  lastOperation.value = '无';
  
  const container = document.getElementById('element-test-container');
  if (container) {
    container.innerHTML = '';
  }
  
  EleMessage.info('测试已清空');
};
</script>

<style scoped>
.element-test {
  padding: 20px;
}

.test-actions {
  margin-bottom: 20px;
}

.test-actions .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.test-info {
  margin: 20px 0;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 4px;
}

.info-item {
  margin-bottom: 8px;
}

.test-results {
  margin: 20px 0;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.result-item {
  padding: 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  font-size: 14px;
}

.result-item.success {
  background: #f0f9ff;
  color: #059669;
}

.result-item.error {
  background: #fef2f2;
  color: #dc2626;
}

.result-item small {
  display: block;
  font-size: 12px;
  opacity: 0.8;
  margin-top: 2px;
}

#element-test-container {
  background: #fff;
  border-radius: 4px;
}
</style>
