/**
 * hiprint 配置工具
 * 用于统一管理 hiprint 的初始化和配置
 */

let hiprintInstance = null;
let isInitialized = false;

/**
 * 初始化 hiprint
 * @param {Object} options 配置选项
 * @returns {Promise} 返回 hiprint 实例
 */
export async function initHiprint(options = {}) {
  try {
    // 如果已经初始化，直接返回实例
    if (isInitialized && hiprintInstance) {
      return hiprintInstance;
    }

    // 动态导入 hiprint
    const { hiprint } = await import('vue-plugin-hiprint');

    // 默认配置
    const defaultConfig = {
      host: '', // 禁用WebSocket连接，避免连接错误
      token: '',
      debug: false,
      ...options
    };

    // 初始化 hiprint
    hiprint.init(defaultConfig);

    hiprintInstance = hiprint;
    isInitialized = true;

    console.log('hiprint 初始化成功');
    return hiprint;

  } catch (error) {
    console.error('hiprint 初始化失败:', error);
    throw new Error('hiprint 插件加载失败：' + error.message);
  }
}

/**
 * 获取 hiprint 实例
 * @returns {Object|null} hiprint 实例
 */
export function getHiprintInstance() {
  return hiprintInstance;
}

/**
 * 检查 hiprint 是否已初始化
 * @returns {boolean} 是否已初始化
 */
export function isHiprintInitialized() {
  return isInitialized;
}

/**
 * 创建打印模板
 * @param {Object} templateData 模板数据
 * @returns {Object} 模板实例
 */
export function createPrintTemplate(templateData = null) {
  if (!hiprintInstance) {
    throw new Error('hiprint 未初始化，请先调用 initHiprint()');
  }

  let template;
  if (templateData) {
    template = new hiprintInstance.PrintTemplate(templateData);
  } else {
    template = new hiprintInstance.PrintTemplate();
  }

  // 添加兼容性方法
  if (!template.addPrintElement && !template.addElement) {
    // 如果没有直接的添加元素方法，尝试通过面板添加
    template.addPrintElement = function(elementConfig) {
      try {
        // 方法1: 检查是否有面板
        if (this.panels && this.panels.length > 0) {
          const panel = this.panels[0];
          if (panel.addPrintElement) {
            return panel.addPrintElement(elementConfig);
          }
        }

        // 方法2: 创建新面板
        const panel = this.addPrintPanel();
        if (panel && panel.addPrintElement) {
          return panel.addPrintElement(elementConfig);
        }

        // 方法3: 直接添加到模板
        if (this.addElement) {
          return this.addElement(elementConfig);
        }

        throw new Error('无法找到添加元素的方法');
      } catch (error) {
        console.error('添加元素失败:', error);
        throw error;
      }
    };
  }

  return template;
}

/**
 * 设计器配置
 */
export const designerConfig = {
  grid: true,
  gridColor: '#ddd',
  gridSize: 5,
  showRuler: true,
  rulerColor: '#999',
  backgroundColor: '#f5f5f5'
};

/**
 * 预览配置
 */
export const previewConfig = {
  preview: true,
  width: '100%',
  height: 'auto'
};

/**
 * 打印配置
 */
export const printConfig = {
  margin: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10
  },
  pageSize: 'A4',
  orientation: 'portrait' // portrait | landscape
};

/**
 * 常用元素模板
 */
export const elementTemplates = {
  text: {
    options: {
      left: 50,
      top: 50,
      height: 30,
      width: 200,
      title: '文本',
      textType: 'text',
      fontSize: 12,
      fontFamily: 'Microsoft YaHei',
      color: '#000000'
    }
  },
  image: {
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '图片',
      src: '',
      fit: 'contain'
    }
  },
  barcode: {
    options: {
      left: 50,
      top: 50,
      height: 50,
      width: 200,
      title: '条形码',
      textType: 'barcode',
      code: 'CODE128',
      value: '123456789'
    }
  },
  qrcode: {
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '二维码',
      textType: 'qrcode',
      value: 'https://example.com'
    }
  },
  line: {
    options: {
      left: 50,
      top: 50,
      height: 1,
      width: 200,
      title: '线条',
      borderStyle: 'solid',
      borderWidth: 1,
      borderColor: '#000000'
    }
  },
  rect: {
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 200,
      title: '矩形',
      borderStyle: 'solid',
      borderWidth: 1,
      borderColor: '#000000',
      backgroundColor: 'transparent'
    }
  }
};

/**
 * 字段类型映射
 */
export const fieldTypeMap = {
  'String': 'text',
  'Integer': 'text',
  'Long': 'text',
  'Double': 'text',
  'BigDecimal': 'text',
  'Date': 'text',
  'LocalDateTime': 'text',
  'Boolean': 'text'
};

/**
 * 获取字段对应的元素模板
 * @param {string} fieldType 字段类型
 * @param {string} fieldName 字段名称
 * @param {string} displayName 显示名称
 * @returns {Object} 元素模板
 */
export function getFieldElementTemplate(fieldType, fieldName, displayName) {
  const elementType = fieldTypeMap[fieldType] || 'text';
  const template = { ...elementTemplates[elementType] };

  // 设置字段相关属性
  template.options.title = displayName || fieldName;
  template.options.field = fieldName;
  template.options.fieldType = fieldType;

  return template;
}

/**
 * 错误处理
 */
export function handleHiprintError(error) {
  console.error('hiprint 错误:', error);

  // 根据错误类型提供不同的处理建议
  if (error.message.includes('WebSocket')) {
    console.warn('WebSocket 连接失败，这是正常的，不影响设计器功能');
    return '打印服务连接失败，但设计器功能正常';
  } else if (error.message.includes('PrintTemplate')) {
    return '模板创建失败，请检查模板数据格式';
  } else if (error.message.includes('design')) {
    return '设计器初始化失败，请刷新页面重试';
  } else {
    return '未知错误：' + error.message;
  }
}

/**
 * 检测 hiprint API
 * @returns {Object} API 信息
 */
export function detectHiprintAPI() {
  if (!hiprintInstance) {
    return { error: 'hiprint 未初始化' };
  }

  const template = new hiprintInstance.PrintTemplate();
  const apiInfo = {
    templateMethods: Object.getOwnPropertyNames(template),
    templatePrototype: Object.getOwnPropertyNames(Object.getPrototypeOf(template)),
    hasAddPrintElement: typeof template.addPrintElement === 'function',
    hasAddElement: typeof template.addElement === 'function',
    hasAddPrintPanel: typeof template.addPrintPanel === 'function',
    hasDesign: typeof template.design === 'function',
    hasGetJson: typeof template.getJson === 'function',
    panels: template.panels || null
  };

  console.log('hiprint API 检测结果:', apiInfo);
  return apiInfo;
}

/**
 * 安全添加元素到模板
 * @param {Object} template 模板实例
 * @param {Object} elementConfig 元素配置
 * @returns {Object} 添加结果
 */
export function safeAddElement(template, elementConfig) {
  try {
    // 方法1: 直接使用 addPrintElement
    if (typeof template.addPrintElement === 'function') {
      const result = template.addPrintElement(elementConfig);
      return { success: true, method: 'addPrintElement', result };
    }

    // 方法2: 使用 addElement
    if (typeof template.addElement === 'function') {
      const result = template.addElement(elementConfig);
      return { success: true, method: 'addElement', result };
    }

    // 方法3: 通过面板添加
    if (typeof template.addPrintPanel === 'function') {
      let panel;

      // 检查是否已有面板
      if (template.panels && template.panels.length > 0) {
        panel = template.panels[0];
      } else {
        panel = template.addPrintPanel();
      }

      if (panel && typeof panel.addPrintElement === 'function') {
        const result = panel.addPrintElement(elementConfig);
        return { success: true, method: 'panel.addPrintElement', result };
      }
    }

    return { success: false, error: '未找到可用的添加元素方法' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 重置 hiprint 状态
 */
export function resetHiprint() {
  hiprintInstance = null;
  isInitialized = false;
}

export default {
  initHiprint,
  getHiprintInstance,
  isHiprintInitialized,
  createPrintTemplate,
  detectHiprintAPI,
  safeAddElement,
  designerConfig,
  previewConfig,
  printConfig,
  elementTemplates,
  fieldTypeMap,
  getFieldElementTemplate,
  handleHiprintError,
  resetHiprint
};
