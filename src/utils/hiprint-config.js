/**
 * hiprint 配置工具
 * 用于统一管理 hiprint 的初始化和配置
 */

let hiprintInstance = null;
let isInitialized = false;

/**
 * 初始化 hiprint
 * @param {Object} options 配置选项
 * @returns {Promise} 返回 hiprint 实例
 */
export async function initHiprint(options = {}) {
  try {
    // 如果已经初始化，直接返回实例
    if (isInitialized && hiprintInstance) {
      return hiprintInstance;
    }

    // 动态导入 hiprint
    const { hiprint } = await import('vue-plugin-hiprint');

    // 默认配置
    const defaultConfig = {
      host: '', // 禁用WebSocket连接，避免连接错误
      token: '',
      debug: false,
      ...options
    };

    // 初始化 hiprint
    hiprint.init(defaultConfig);

    hiprintInstance = hiprint;
    isInitialized = true;

    console.log('hiprint 初始化成功');
    return hiprint;

  } catch (error) {
    console.error('hiprint 初始化失败:', error);
    throw new Error('hiprint 插件加载失败：' + error.message);
  }
}

/**
 * 获取 hiprint 实例
 * @returns {Object|null} hiprint 实例
 */
export function getHiprintInstance() {
  return hiprintInstance;
}

/**
 * 检查 hiprint 是否已初始化
 * @returns {boolean} 是否已初始化
 */
export function isHiprintInitialized() {
  return isInitialized;
}

/**
 * 创建打印模板
 * @param {Object} templateData 模板数据
 * @returns {Object} 模板实例
 */
export function createPrintTemplate(templateData = null) {
  if (!hiprintInstance) {
    throw new Error('hiprint 未初始化，请先调用 initHiprint()');
  }

  let template;
  if (templateData) {
    template = new hiprintInstance.PrintTemplate(templateData);
  } else {
    template = new hiprintInstance.PrintTemplate();
  }

  return template;
}

/**
 * 设计器配置
 */
export const designerConfig = {
  grid: true,
  gridColor: '#ddd',
  gridSize: 5,
  showRuler: true,
  rulerColor: '#999',
  backgroundColor: '#f5f5f5'
};

/**
 * 预览配置
 */
export const previewConfig = {
  preview: true,
  width: '100%',
  height: 'auto'
};

/**
 * 打印配置
 */
export const printConfig = {
  margin: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10
  },
  pageSize: 'A4',
  orientation: 'portrait' // portrait | landscape
};

/**
 * 常用元素模板 - 基于 hiprint 标准格式
 */
export const elementTemplates = {
  text: {
    tid: 'text',
    title: '文本',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 30,
      width: 200,
      title: '文本',
      text: '文本内容',
      fontSize: 14,
      fontFamily: 'Microsoft YaHei',
      color: '#000000',
      textAlign: 'left',
      lineHeight: 1.2,
      backgroundColor: '#ffffff',
      borderWidth: 1,
      borderStyle: 'solid',
      borderColor: '#cccccc'
    }
  },
  image: {
    tid: 'image',
    title: '图片',
    type: 'image',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '图片',
      src: '',
      fit: 'contain'
    }
  },
  barcode: {
    tid: 'barcode',
    title: '条形码',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 50,
      width: 200,
      title: '条形码',
      text: '123456789',
      textType: 'barcode',
      code: 'CODE128',
      fontSize: 12
    }
  },
  qrcode: {
    tid: 'qrcode',
    title: '二维码',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '二维码',
      text: 'https://example.com',
      textType: 'qrcode'
    }
  },
  line: {
    tid: 'line',
    title: '线条',
    type: 'hline',
    options: {
      left: 50,
      top: 50,
      height: 1,
      width: 200,
      title: '线条'
    }
  },
  rect: {
    tid: 'rect',
    title: '矩形',
    type: 'rect',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 200,
      title: '矩形'
    }
  }
};

/**
 * 字段类型映射
 */
export const fieldTypeMap = {
  'String': 'text',
  'Integer': 'text',
  'Long': 'text',
  'Double': 'text',
  'BigDecimal': 'text',
  'Date': 'text',
  'LocalDateTime': 'text',
  'Boolean': 'text'
};

/**
 * 获取字段对应的元素模板
 * @param {string} fieldType 字段类型
 * @param {string} fieldName 字段名称
 * @param {string} displayName 显示名称
 * @returns {Object} 元素模板
 */
export function getFieldElementTemplate(fieldType, fieldName, displayName) {
  const elementType = fieldTypeMap[fieldType] || 'text';
  const template = { ...elementTemplates[elementType] };

  // 设置字段相关属性
  template.options.title = displayName || fieldName;
  template.options.field = fieldName;
  template.options.fieldType = fieldType;

  return template;
}

/**
 * 错误处理
 */
export function handleHiprintError(error) {
  console.error('hiprint 错误:', error);

  // 根据错误类型提供不同的处理建议
  if (error.message.includes('WebSocket')) {
    console.warn('WebSocket 连接失败，这是正常的，不影响设计器功能');
    return '打印服务连接失败，但设计器功能正常';
  } else if (error.message.includes('PrintTemplate')) {
    return '模板创建失败，请检查模板数据格式';
  } else if (error.message.includes('design')) {
    return '设计器初始化失败，请刷新页面重试';
  } else {
    return '未知错误：' + error.message;
  }
}

/**
 * 检测 hiprint API
 * @returns {Object} API 信息
 */
export function detectHiprintAPI() {
  if (!hiprintInstance) {
    return { error: 'hiprint 未初始化' };
  }

  const template = new hiprintInstance.PrintTemplate();
  const apiInfo = {
    templateMethods: Object.getOwnPropertyNames(template),
    templatePrototype: Object.getOwnPropertyNames(Object.getPrototypeOf(template)),
    hasAddPrintElement: typeof template.addPrintElement === 'function',
    hasAddElement: typeof template.addElement === 'function',
    hasAddPrintPanel: typeof template.addPrintPanel === 'function',
    hasDesign: typeof template.design === 'function',
    hasGetJson: typeof template.getJson === 'function',
    panels: template.panels || null
  };

  console.log('hiprint API 检测结果:', apiInfo);
  return apiInfo;
}

/**
 * 安全添加元素到模板
 * @param {Object} template 模板实例
 * @param {Object} elementConfig 元素配置
 * @returns {Object} 添加结果
 */
export function safeAddElement(template, elementConfig) {
  console.log('开始添加元素:', elementConfig);

  try {
    // 确保有面板存在
    let panel = null;

    // 检查是否已有面板
    if (template.panels && Array.isArray(template.panels) && template.panels.length > 0) {
      panel = template.panels[0];
      console.log('使用现有面板:', panel);
    } else {
      // 创建新面板 - 这是 hiprint 的标准做法
      if (typeof template.addPrintPanel === 'function') {
        panel = template.addPrintPanel();
        console.log('创建新面板:', panel);
      }
    }

    if (!panel) {
      return { success: false, error: '无法创建或获取面板' };
    }

    // 根据元素类型使用不同的添加方法
    const elementType = elementConfig.type || elementConfig.tid || 'text';
    console.log('元素类型:', elementType);

    let result = null;

    // 尝试使用特定类型的添加方法
    switch (elementType) {
      case 'text':
        if (typeof panel.addPrintText === 'function') {
          result = panel.addPrintText(elementConfig);
          console.log('使用 addPrintText 成功:', result);

          // 验证元素是否真的添加到面板中
          console.log('面板元素数量:', panel.printElements ? panel.printElements.length : 'unknown');
          console.log('模板面板数量:', template.panels.length);

          // 尝试刷新设计器显示
          if (typeof template.refresh === 'function') {
            template.refresh();
            console.log('模板已刷新');
          }

          return { success: true, method: 'panel.addPrintText', result };
        }
        break;

      case 'image':
        if (typeof panel.addPrintImage === 'function') {
          result = panel.addPrintImage(elementConfig);
          console.log('使用 addPrintImage 成功:', result);
          return { success: true, method: 'panel.addPrintImage', result };
        }
        break;

      case 'hline':
      case 'line':
        if (typeof panel.addPrintHline === 'function') {
          result = panel.addPrintHline(elementConfig);
          console.log('使用 addPrintHline 成功:', result);
          return { success: true, method: 'panel.addPrintHline', result };
        }
        break;

      case 'rect':
        if (typeof panel.addPrintRect === 'function') {
          result = panel.addPrintRect(elementConfig);
          console.log('使用 addPrintRect 成功:', result);
          return { success: true, method: 'panel.addPrintRect', result };
        }
        break;
    }

    // 如果特定方法不存在，尝试通用方法
    if (typeof panel.addPrintElement === 'function') {
      result = panel.addPrintElement(elementConfig);
      console.log('使用 addPrintElement 成功:', result);
      return { success: true, method: 'panel.addPrintElement', result };
    }

    if (typeof panel.addElement === 'function') {
      result = panel.addElement(elementConfig);
      console.log('使用 addElement 成功:', result);
      return { success: true, method: 'panel.addElement', result };
    }

    // 最后尝试直接在模板上添加
    if (typeof template.addPrintElement === 'function') {
      result = template.addPrintElement(elementConfig);
      console.log('使用 template.addPrintElement 成功:', result);
      return { success: true, method: 'template.addPrintElement', result };
    }

    return {
      success: false,
      error: '未找到可用的添加元素方法',
      debug: {
        elementType,
        panelMethods: Object.getOwnPropertyNames(panel),
        templateMethods: Object.getOwnPropertyNames(template)
      }
    };

  } catch (error) {
    console.error('添加元素时发生错误:', error);
    return { success: false, error: error.message, stack: error.stack };
  }
}

/**
 * 重置 hiprint 状态
 */
export function resetHiprint() {
  hiprintInstance = null;
  isInitialized = false;
}

export default {
  initHiprint,
  getHiprintInstance,
  isHiprintInitialized,
  createPrintTemplate,
  detectHiprintAPI,
  safeAddElement,
  designerConfig,
  previewConfig,
  printConfig,
  elementTemplates,
  fieldTypeMap,
  getFieldElementTemplate,
  handleHiprintError,
  resetHiprint
};
